token-manager.js:173 [TOKEN MANAGER] Token Manager inizializzato
game:460 [STATE] Dati caricati da sessionStorage e rimossi
game:480 [STATE] Controllo iniziale:
game:481 [STATE] - skipAnimations: true
game:482 [STATE] - fullInterface: true
game:483 [STATE] - fromSessionStorage: true
game:484 [STATE] - savedState: null
game:490 [STATE] - shouldShowFullInterface: true
game:500 [STATE] - hasActiveGame: false
game:501 [STATE] - shouldDefaultToFullInterface: false
game:509 [GAME] Dati da sessionStorage: attivo interfaccia completa
script.js:2949 [DRAG FIX] Removed setup-animation element from DOM
script.js:1380 [SOCKET INIT] Creating socket with token: Present
script.js:1358 [SOCKET HANDLERS] Handler socket aggiuntivi inizializzati
local-game.js:198 [LOCAL GAME] Manager caricato e pronto
online-game.js:343 [ONLINE GAME] Manager caricato e pronto
game-mode-manager.js:218 [GAME MODE] Manager principale caricato e pronto
player-names-protection.js:325 [NAMES PROTECTION] Sistema di protezione nomi caricato
smooth-loading.js:89 [SMOOTH LOADING] Inizializzazione sistema loading fluido
smooth-loading.js:205 [SMOOTH LOADING] Sistema di loading fluido caricato
game:518 [GAME] DOM caricato, mostro interfaccia completa
game:534 [GAME] Game container mostrato con interfaccia completa
game:568 [STATE] Stato salvato: Object
game:835 [STATE] Dati da sessionStorage, URL già pulito
script.js:2854 [PRELOAD] Avvio precaricamento immagini delle carte: Array(4)
script.js:3332 [PLAYER NAMES] Modalità locale - Nome del giocatore 1 impostato a: giggio
script.js:15072 [INIT] Pagina di gioco rilevata, evito fullResetGameUI per prevenire refresh continui
script.js:16831 [PSN] Inizializzazione sistema PSN...
multiplayer.js:1772 [MULTIPLAYER] DOMContentLoaded - Inizio inizializzazione
multiplayer.js:1773 [MULTIPLAYER] URL: http://localhost:3000/game
multiplayer.js:234 [CHAT] DEBUG: Chat event listener sarà registrato nell'evento connect quando socket è connesso
multiplayer.js:235 [CHAT] DEBUG: Socket ID in initSocketEvents: undefined (potrebbe essere undefined)
multiplayer.js:1977 [CHAT] DEBUG: Socket presente ma potrebbe non essere ancora connesso. Socket ID: undefined
multiplayer.js:1978 [CHAT] Event listener per chatMessage sarà registrato quando il socket è connesso
multiplayer.js:1704 [HAND SLOTS] Creazione slot per le carte nelle aree delle mani
multiplayer.js:1725 [HAND SLOTS] Creati slot per la mano del giocatore 1
multiplayer.js:1745 [HAND SLOTS] Creati slot per la mano del giocatore 2
multiplayer.js:1926 [ANIMATION] Osservatore impostato per rilevare il completamento dell'animazione
online-play-enhancer.js:86 [ONLINE ENHANCER] DOM pronto, inizializzo enhancer...
victory-fix.js:158 victory-fix.js caricato.
script.js:2859 [PRELOAD] SUCCESSO precaricamento 1/4: http://localhost:3000/img/carte/card-back.webp
script.js:2859 [PRELOAD] SUCCESSO precaricamento 4/4: http://localhost:3000/img/Cover carte/cover.png
script.js:1758 [SOCKET CONNECT] myPlayerId attuale: 8KRo3pQrmdKA009sAAAT socket.id: 8KRo3pQrmdKA009sAAAT
script.js:1762 [CHAT] DEBUG: Registrando event listener per chatMessage su socket connesso. Socket ID: 8KRo3pQrmdKA009sAAAT
script.js:1777 [CHAT] Event listener per chatMessage registrato su socket connesso
script.js:1781 [CHAT] DEBUG: Numero di listener per chatMessage: 1
script.js:2859 [PRELOAD] SUCCESSO precaricamento 2/4: http://localhost:3000/img/carte/card-back.png
script.js:2859 [PRELOAD] SUCCESSO precaricamento 3/4: http://localhost:3000/img/Cover carte/cover.webp
script.js:5245 [BOARD CREATION] Creazione nuovo tabellone di gioco - Stack trace: Error
    at createGameBoard (http://localhost:3000/script.js:5245:87)
    at http://localhost:3000/game:540:29
game:541 [GAME] Tabellone di gioco creato
multiplayer.js:1704 [HAND SLOTS] Creazione slot per le carte nelle aree delle mani
multiplayer.js:1725 [HAND SLOTS] Creati slot per la mano del giocatore 1
multiplayer.js:1745 [HAND SLOTS] Creati slot per la mano del giocatore 2
game:549 [GAME] Slot mani giocatori creati
smooth-loading.js:17 [SMOOTH LOADING] Avvio animazioni di caricamento
smooth-loading.js:58 [SMOOTH LOADING] Animazioni di caricamento completate
game:594 [GAME] Tab "Nuova Partita" attivato
game:599 [GAME] Contenuto "Nuova Partita" mostrato
inspector.b9415ea5.js:12 Could not access stylesheet rules: SecurityError: Failed to read the 'cssRules' property from 'CSSStyleSheet': Cannot access rules
    at inspector.b9415ea5.js:12:57303
    at Array.forEach (<anonymous>)
    at n (inspector.b9415ea5.js:12:57152)
    at g (inspector.b9415ea5.js:12:55110)
    at 1MLsW.@reduxjs/toolkit (inspector.b9415ea5.js:12:1920)
    at u (inspector.b9415ea5.js:1:705)
    at d (inspector.b9415ea5.js:1:809)
    at fLzap../distance (inspector.b9415ea5.js:1:153421)
    at u (inspector.b9415ea5.js:1:705)
    at d (inspector.b9415ea5.js:1:809)
(anonime) @ inspector.b9415ea5.js:12
ads.914af30a.js:1 Ads initialization already in progress or completed
ads.914af30a.js:1 Ads initialization already in progress or completed
ads.914af30a.js:1 Ads initialization already in progress or completed
inspector.b9415ea5.js:1 Error: Minified React error #31; visit https://reactjs.org/docs/error-decoder.html?invariant=31&args[]=%5Bobject%20Promise%5D for the full message or use the non-minified dev environment for full errors and additional helpful warnings.
    at n0 (inspector.b9415ea5.js:1:65377)
    at l (inspector.b9415ea5.js:1:70370)
    at ol (inspector.b9415ea5.js:1:82233)
    at i (inspector.b9415ea5.js:1:130901)
    at lM (inspector.b9415ea5.js:1:110198)
    at inspector.b9415ea5.js:1:110064
    at lO (inspector.b9415ea5.js:1:110072)
    at lC (inspector.b9415ea5.js:1:106823)
    at lw (inspector.b9415ea5.js:1:105378)
    at C (inspector.b9415ea5.js:1:141367)
s7 @ inspector.b9415ea5.js:1
inspector.b9415ea5.js:1 Uncaught Error: Minified React error #31; visit https://reactjs.org/docs/error-decoder.html?invariant=31&args[]=%5Bobject%20Promise%5D for the full message or use the non-minified dev environment for full errors and additional helpful warnings.
    at n0 (inspector.b9415ea5.js:1:65377)
    at l (inspector.b9415ea5.js:1:70370)
    at ol (inspector.b9415ea5.js:1:82233)
    at i (inspector.b9415ea5.js:1:130901)
    at lM (inspector.b9415ea5.js:1:110198)
    at inspector.b9415ea5.js:1:110064
    at lO (inspector.b9415ea5.js:1:110072)
    at lC (inspector.b9415ea5.js:1:106823)
    at lw (inspector.b9415ea5.js:1:105378)
    at C (inspector.b9415ea5.js:1:141367)
ads.914af30a.js:1 Attempting to initialize AdUnit
ads.914af30a.js:1 AdUnit initialized successfully
ads.914af30a.js:1 Ads initialized successfully for: http://localhost:3000/game
ads.914af30a.js:1 Ads initialization already in progress or completed
online-play-enhancer.js:200 [ONLINE ENHANCER] Finestra completamente caricata
online-play-enhancer.js:208 [ONLINE ENHANCER] Pulsante Gioca Online non cliccato, non applico stili al caricamento
script.js:90 [PERSISTENCE] Stato salvato scaduto o non presente
script.js:158 [PERSISTENCE] Stato salvato rimosso
script.js:2204 [VISIBILITY] Browser minimizzato - fermando tutti i suoni card.mp3
script.js:2225 [VISIBILITY] Istanza cache cardDealingAmbient fermata
script.js:2243 [VISIBILITY] Tutti i suoni card.mp3 fermati con successo
script.js:2573 [VISIBILITY] Pagina nascosta, preparo per riavvio animazioni
player-names-protection.js:25 [NAMES PROTECTION] Salvato nome player 1: Giocatore 1
player-names-protection.js:32 [NAMES PROTECTION] Salvato nome player 2: Giocatore 2
player-names-protection.js:123 [NAMES PROTECTION] Protezione attivata
player-names-protection.js:25 [NAMES PROTECTION] Salvato nome player 1: Giocatore 1
player-names-protection.js:32 [NAMES PROTECTION] Salvato nome player 2: Giocatore 2
game:1 Uncaught (in promise) Error: A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received
game:1 Uncaught (in promise) Error: A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received
game:1 Uncaught (in promise) Error: A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received
game:1 Uncaught (in promise) Error: A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received
game:1 Uncaught (in promise) Error: A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received
game:1 Uncaught (in promise) Error: A listener indicated an asynchronous response by returning true, but the message channel closed before a response was received
script.js:2371 [VISIBILITY] Pagina tornata visibile, controllo animazioni in sospeso
script.js:2374 [VISIBILITY] Eseguo pulizia cover cards bloccate...
psn-unified.js:873 [PSN] Browser tornato visibile - controllo sincronizzazione PSN
script.js:2300 [VISIBILITY] Flag setup interrotto resettato
game:606 [GAME] Pulsante Nuova Partita cliccato
game:612 [GAME] Modalità: online Tempo: none
game:621 [MATCHMAKING] Avvio matchmaking online
game:640 [MATCHMAKING] Socket già connesso
game:660 [MATCHMAKING] Socket connesso, invio findMatch
game:718 [MATCHMAKING] Invio findMatch con dati: Object
smooth-loading.js:63 [SMOOTH LOADING] Gestendo transizione matchmaking
game:665 [MATCHMAKING] Match trovato! Object
game:672 [MATCHMAKING] Salvando dati partita con gameId: 5H31KK
game:438 [STATE] Pulizia stato
game:677 [MATCHMAKING] Stato interfaccia completa pulito
game:864 [STATE] Partita iniziata, pulizia stato interfaccia completa
game:865 [STATE] Dettagli partita: Object
game:438 [STATE] Pulizia stato
script.js:1166 [SOCKET] Ricevuto evento gameState: Object
script.js:1170 [SOCKET] Chiamando handleGameStateEvent
player-names-protection.js:218 [NAMES PROTECTION] Tentativo di modificare player1 textContent: giggio
player-names-protection.js:239 [NAMES PROTECTION] Tentativo di modificare player2 textContent: bruscolino
script.js:14219 [PLAYER AREAS] Attivata animazione glow aggiungendo classe ready-for-play
script.js:11352 [GAME STATE] Prima chiamata a showGameSetup
script.js:11362 [GAME STATE] Partita multiplayer - preparazione setup senza visualizzazione
script.js:3843 [GAME SETUP] showGameSetup() chiamato
script.js:3866 [GAME SETUP] Modalità transizione fluida - evito reset visuale
script.js:3881 [GAME SETUP] Transizione fluida - skip visualizzazione container
game:735 [SIDEBAR] Riabilitazione di tutti i tab
game:741 [SIDEBAR] Tab riabilitato: gioca
game:741 [SIDEBAR] Tab riabilitato: nuova-partita
game:741 [SIDEBAR] Tab riabilitato: analisi
game:741 [SIDEBAR] Tab riabilitato: giocatori
game:747 [SIDEBAR] Passaggio al tab Gioca
game:765 [SIDEBAR] Tab "Gioca" attivato
game:772 [SIDEBAR] Contenuto "Gioca" mostrato
game:683 [MATCHMAKING] Passaggio al tab Gioca completato
game:697 [MATCHMAKING] Iniziando partita online senza refresh
game:701 [MATCHMAKING] Chiamando handleMatchFound per iniziare il flusso di gioco
multiplayer.js:502 [MATCH FOUND] Reset flags protezione turno per nuova partita
online-play-enhancer.js:214 [ONLINE ENHANCER] Applicazione IMMEDIATA della classe online-play-interface
online-play-enhancer.js:219 [ONLINE ENHANCER] IMMEDIATO: CSS di emergenza già presente nel file CSS
online-play-enhancer.js:231 [ONLINE ENHANCER] IMMEDIATO: Classe applicata alla players-column
online-play-enhancer.js:234 [ONLINE ENHANCER] IMMEDIATO: Classe applicata, stili gestiti dai CSS
multiplayer.js:1704 [HAND SLOTS] Creazione slot per le carte nelle aree delle mani
multiplayer.js:1725 [HAND SLOTS] Creati slot per la mano del giocatore 1
multiplayer.js:1745 [HAND SLOTS] Creati slot per la mano del giocatore 2
multiplayer.js:1942 [RATING] Rating avversario aggiornato nella UI: 1000
multiplayer.js:641 [CHAT] Chat abilitata per il multiplayer
game-mode-manager.js:62 [GAME MODE] Manager inizializzato
game-mode-manager.js:86 [GAME MODE] Avvio partita online
online-game.js:58 [ONLINE GAME] Creazione slot per le carte nelle aree delle mani
multiplayer.js:1704 [HAND SLOTS] Creazione slot per le carte nelle aree delle mani
multiplayer.js:1725 [HAND SLOTS] Creati slot per la mano del giocatore 1
multiplayer.js:1745 [HAND SLOTS] Creati slot per la mano del giocatore 2
multiplayer.js:691 [MATCH FOUND] Preparo container senza mostrarlo - sarà visualizzato quando arriva lo stato
multiplayer.js:699 [MATCH FOUND] Container preparato ma nascosto per evitare refresh doppio
multiplayer.js:710 [MATCH FOUND] Setup già completato, skip
multiplayer.js:730 [MATCH FOUND] Socket configurato: Object
multiplayer.js:745 [MATCH FOUND] Entrando nella room del gioco con dati: Object
multiplayer.js:792 [MATCH FOUND] Match trovato e processato: Object
multiplayer.js:2178 [CHAT] DEBUG: Verifica event listener per chatMessage. Socket ID: 8KRo3pQrmdKA009sAAAT
multiplayer.js:2180 [CHAT] DEBUG: Event listener attuali per chatMessage: 1
multiplayer.js:763 [MATCH FOUND] Richiesta stato del gioco con info complete: Object
script.js:1166 [SOCKET] Ricevuto evento gameState: Object
script.js:1170 [SOCKET] Chiamando handleGameStateEvent
multiplayer.js:789 [MATCH FOUND] Flag processingGameData pulito
multiplayer.js:769 [MATCH FOUND] Secondo tentativo di richiesta stato (nessuno stato ricevuto): Object
script.js:1166 [SOCKET] Ricevuto evento gameState: Object
script.js:1170 [SOCKET] Chiamando handleGameStateEvent
script.js:2204 [VISIBILITY] Browser minimizzato - fermando tutti i suoni card.mp3
script.js:2225 [VISIBILITY] Istanza cache cardDealingAmbient fermata
script.js:2243 [VISIBILITY] Tutti i suoni card.mp3 fermati con successo
script.js:2573 [VISIBILITY] Pagina nascosta, preparo per riavvio animazioni
script.js:2371 [VISIBILITY] Pagina tornata visibile, controllo animazioni in sospeso
script.js:2374 [VISIBILITY] Eseguo pulizia cover cards bloccate...
psn-unified.js:873 [PSN] Browser tornato visibile - controllo sincronizzazione PSN
script.js:2300 [VISIBILITY] Flag setup interrotto resettato
script.js:2204 [VISIBILITY] Browser minimizzato - fermando tutti i suoni card.mp3
script.js:2225 [VISIBILITY] Istanza cache cardDealingAmbient fermata
script.js:2243 [VISIBILITY] Tutti i suoni card.mp3 fermati con successo
script.js:2573 [VISIBILITY] Pagina nascosta, preparo per riavvio animazioni
script.js:2371 [VISIBILITY] Pagina tornata visibile, controllo animazioni in sospeso
script.js:2374 [VISIBILITY] Eseguo pulizia cover cards bloccate...
psn-unified.js:873 [PSN] Browser tornato visibile - controllo sincronizzazione PSN
script.js:2300 [VISIBILITY] Flag setup interrotto resettato
